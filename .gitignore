# Security-focused .gitignore for worm development

# Compiled payloads and executables
*.exe
*.dll
*.so
*.dylib
*.bin
*.elf

# Logs and sensitive data
logs/*.log
*.log
c2/database/*.db
c2/database/*.sqlite
c2/database/*.sqlite3

# Tor configuration and keys
c2/tor/hidden_service/
c2/tor/torrc.local
c2/tor/*.key
c2/tor/*.onion

# C2 server data
c2/sliver/*.log
c2/sliver/configs/
c2/sliver/certs/
c2/empire/*.log
c2/empire/data/
c2/empire/downloads/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Credentials and keys
*.key
*.pem
*.crt
*.p12
*.pfx
credentials.txt
passwords.txt
secrets.txt

# Test artifacts
test_results/
coverage/
.coverage
.pytest_cache/
.tox/

# Payload staging
payloads/staged/
payloads/compiled/
