
pid /home/<USER>/Documents/augment-projects/wormv2/logs/nginx.pid;
error_log /home/<USER>/Documents/augment-projects/wormv2/logs/nginx_error.log;

events {
    worker_connections 1024;
}

http {
    access_log /home/<USER>/Documents/augment-projects/wormv2/logs/nginx_access.log;
    client_body_temp_path /home/<USER>/Documents/augment-projects/wormv2/logs/nginx_client_temp;
    proxy_temp_path /home/<USER>/Documents/augment-projects/wormv2/logs/nginx_proxy_temp;
    fastcgi_temp_path /home/<USER>/Documents/augment-projects/wormv2/logs/nginx_fastcgi_temp;
    uwsgi_temp_path /home/<USER>/Documents/augment-projects/wormv2/logs/nginx_uwsgi_temp;
    scgi_temp_path /home/<USER>/Documents/augment-projects/wormv2/logs/nginx_scgi_temp;

    server {
        listen 8080;
        server_name localhost;

        location / {
            root /home/<USER>/Documents/augment-projects/wormv2/payloads;
            autoindex on;
            add_header Access-Control-Allow-Origin *;
        }

        location /api {
            return 200 "C2 API Endpoint";
            add_header Content-Type text/plain;
        }
    }

    server {
        listen 8443 ssl;
        server_name localhost;

        ssl_certificate /home/<USER>/Documents/augment-projects/wormv2/config/server.crt;
        ssl_certificate_key /home/<USER>/Documents/augment-projects/wormv2/config/server.key;

        location / {
            root /home/<USER>/Documents/augment-projects/wormv2/payloads;
            autoindex on;
        }
    }
}
