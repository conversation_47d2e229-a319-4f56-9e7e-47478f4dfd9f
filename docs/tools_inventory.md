# Penetration Testing Tools Inventory

## Command & Control Infrastructure
- **Sliver**: `/usr/bin/sliver-server`, `/usr/bin/sliver-client` - Modern C2 framework
- **Empire**: `/usr/bin/powershell-empire` - PowerShell post-exploitation framework
- **Tor**: `/usr/sbin/tor` - Anonymous communication network

## Network Discovery & Reconnaissance
- **Nmap**: `/usr/bin/nmap` - Network discovery and security auditing
- **Masscan**: Available for ultra-fast port scanning
- **Autorecon**: Automated reconnaissance tool

## Credential Attacks
- **John the Ripper**: `/usr/bin/john` - Password cracking tool
- **Hashcat**: Available for GPU-accelerated password cracking
- **Hydra**: Available for online brute force attacks
- **CrackMapExec**: Available for multi-protocol network attacks

## Lateral Movement & Exploitation
- **Impacket Suite**: Multiple tools available
  - `/usr/bin/impacket-atexec` - AT command execution
  - `/usr/bin/impacket-dpapi` - DPAPI attacks
  - `/usr/bin/impacket-ping` - SMB ping
  - `/usr/bin/impacket-exchanger` - Exchange attacks
- **Socat**: `/usr/bin/socat` - Network relay tool
- **Chisel**: Available for TCP/UDP tunneling
- **ProxyChains**: Available for connection proxying

## Web Exploitation
- **Gobuster**: Available for directory discovery
- **SQLMap**: Available for SQL injection exploitation
- **Nikto**: Available for web vulnerability scanning
- **Burp Suite**: Available for web application testing

## Social Engineering
- **GoPhish**: Available for phishing campaigns
- **Social Engineer Toolkit**: Available for automated SE
- **King Phisher**: Available for advanced phishing

## Stealth & Anti-Forensics
- **Timestomp**: Available for file timestamp manipulation
- **Shred**: Available for secure file deletion
- **BleachBit**: Available for system cleaning

## Persistence Mechanisms
- **Crontab**: System cron job management
- **Systemd**: Service creation and management
- **Registry Tools**: Windows registry manipulation
- **Scheduled Tasks**: Windows task scheduling

## Database & Storage
- **SQLite3**: `/usr/bin/sqlite3` - Lightweight database
- **PostgreSQL**: Available for advanced database needs

## Web Servers
- **Nginx**: `/usr/sbin/nginx` - High-performance web server
- **Apache2**: `/usr/sbin/apache2` - Traditional web server

## Development Tools
- **Python3**: `/usr/bin/python3` - Primary scripting language
- **Git**: `/usr/bin/git` - Version control system

## Capability Matrix

| Category | Tool Count | Primary Use |
|----------|------------|-------------|
| C2 Infrastructure | 3 | Command & Control |
| Network Discovery | 10+ | Reconnaissance |
| Credential Attacks | 15+ | Password/Hash Cracking |
| Lateral Movement | 20+ | Network Exploitation |
| Web Exploitation | 10+ | Web Application Testing |
| Social Engineering | 5+ | Human Factor Attacks |
| Stealth/Anti-Forensics | 10+ | Detection Evasion |
| Persistence | 15+ | Maintaining Access |

## Tool Integration Strategy
All tools will be orchestrated through Python scripts that:
1. Detect target OS and environment
2. Select appropriate tool combinations
3. Execute tools with proper parameters
4. Parse and correlate results
5. Maintain operational security throughout
