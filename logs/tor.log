Jun 12 18:35:44.033 [notice] Tor ******** opening new log file.
Jun 12 18:35:44.031 [notice] We compiled with OpenSSL 101010ef: OpenSSL 1.1.1n  15 Mar 2022 and we are running with OpenSSL 1010117f: 1.1.1w. These two versions should be binary compatible.
Jun 12 18:35:44.031 [notice] Tor ******** running on Linux with Libevent 2.1.12-stable, OpenSSL 1.1.1w, Zlib 1.3.1, Liblzma 5.8.1, Libzstd 1.5.7 and Glibc 2.41 as libc.
Jun 12 18:35:44.031 [notice] Tor can't help you if you use it wrong! Learn how to be safe at https://support.torproject.org/faq/staying-anonymous/
Jun 12 18:35:44.031 [warn] Tor was compiled with zstd 1.4.8, but is running with zstd 1.5.7. For safety, we'll avoid using advanced zstd functionality.
Jun 12 18:35:44.031 [notice] Read configuration file "/home/<USER>/Documents/augment-projects/wormv2/c2/tor/torrc".
Jun 12 18:35:44.032 [notice] Converting FascistFirewall and FirewallPorts config options to new format: "ReachableAddresses *:80,*:443,*:9050,*:9051"
Jun 12 18:35:44.033 [notice] Wow!  I detected that you have 24 CPUs. I will not autodetect any more than 16, though.  If you want to configure more, set NumCPUs in your torrc
Jun 12 18:35:44.033 [notice] Opening Socks listener on 127.0.0.1:9050
Jun 12 18:35:44.033 [notice] Opened Socks listener connection (ready) on 127.0.0.1:9050
Jun 12 18:35:44.033 [notice] Opening Control listener on 127.0.0.1:9051
Jun 12 18:35:44.033 [notice] Opened Control listener connection (ready) on 127.0.0.1:9051
Jun 12 18:35:44.033 [warn] Your log may contain sensitive information - you disabled SafeLogging. Don't log unless it serves an important reason. Overwrite the log afterwards.
Jun 12 18:35:44.038 [warn] Unhandled OpenSSL errors found at ../src/lib/tls/tortls.c:190: 
Jun 12 18:35:44.038 [warn] TLS error: could not load the shared library (in DSO support routines:dlfcn_load:---)
Jun 12 18:35:44.038 [warn] TLS error: could not load the shared library (in DSO support routines:DSO_load:---)
Jun 12 18:35:44.038 [warn] TLS error: error loading dso (in configuration file routines:module_load_dso:---)
Jun 12 18:35:44.038 [warn] TLS error: unknown module name (in configuration file routines:module_run:---)
Jun 12 18:35:44.038 [notice] Bootstrapped 0% (starting): Starting
Jun 12 18:35:44.038 [notice] Starting with guard context "default"
Jun 12 18:35:45.039 [notice] Bootstrapped 5% (conn): Connecting to a relay
Jun 12 18:35:46.112 [notice] Bootstrapped 10% (conn_done): Connected to a relay
Jun 12 18:35:46.195 [notice] Bootstrapped 14% (handshake): Handshaking with a relay
Jun 12 18:35:46.400 [notice] Bootstrapped 15% (handshake_done): Handshake with a relay done
Jun 12 18:35:46.400 [notice] Bootstrapped 20% (onehop_create): Establishing an encrypted directory connection
Jun 12 18:35:46.470 [notice] Bootstrapped 25% (requesting_status): Asking for networkstatus consensus
Jun 12 18:35:46.539 [notice] Bootstrapped 30% (loading_status): Loading networkstatus consensus
Jun 12 18:35:47.159 [notice] I learned some more directory information, but not enough to build a circuit: We have no usable consensus.
Jun 12 18:35:47.237 [notice] Bootstrapped 40% (loading_keys): Loading authority key certs
Jun 12 18:35:47.358 [notice] The current consensus has no exit nodes. Tor can only build internal paths, such as paths to onion services.
Jun 12 18:35:47.359 [notice] Bootstrapped 45% (requesting_descriptors): Asking for relay descriptors
Jun 12 18:35:47.359 [notice] I learned some more directory information, but not enough to build a circuit: We need more microdescriptors: we have 0/8489, and can only build 0% of likely paths. (We have 0% of guards bw, 0% of midpoint bw, and 0% of end bw (no exits in consensus, using mid) = 0% of path bw.)
Jun 12 18:35:47.683 [notice] Bootstrapped 50% (loading_descriptors): Loading relay descriptors
Jun 12 18:35:48.003 [notice] The current consensus contains exit nodes. Tor can build exit and internal paths.
Jun 12 18:35:49.344 [notice] Bootstrapped 56% (loading_descriptors): Loading relay descriptors
Jun 12 18:35:49.384 [notice] Bootstrapped 61% (loading_descriptors): Loading relay descriptors
Jun 12 18:35:49.423 [notice] Bootstrapped 67% (loading_descriptors): Loading relay descriptors
Jun 12 18:35:49.442 [notice] Bootstrapped 72% (loading_descriptors): Loading relay descriptors
Jun 12 18:35:49.464 [notice] Bootstrapped 75% (enough_dirinfo): Loaded enough directory info to build circuits
Jun 12 18:35:50.048 [notice] Bootstrapped 80% (ap_conn): Connecting to a relay to build circuits
Jun 12 18:35:50.088 [notice] Bootstrapped 85% (ap_conn_done): Connected to a relay to build circuits
Jun 12 18:35:50.135 [notice] Bootstrapped 89% (ap_handshake): Finishing handshake with a relay to build circuits
Jun 12 18:35:50.247 [notice] Bootstrapped 90% (ap_handshake_done): Handshake finished with a relay to build circuits
Jun 12 18:35:50.247 [notice] Bootstrapped 95% (circuit_create): Establishing a Tor circuit
Jun 12 18:35:50.572 [notice] Bootstrapped 100% (done): Done
Jun 12 18:35:58.990 [notice] Catching signal TERM, exiting cleanly.
Jun 12 18:36:05.023 [notice] Tor ******** opening log file.
Jun 12 18:36:05.021 [notice] We compiled with OpenSSL 101010ef: OpenSSL 1.1.1n  15 Mar 2022 and we are running with OpenSSL 1010117f: 1.1.1w. These two versions should be binary compatible.
Jun 12 18:36:05.022 [notice] Tor ******** running on Linux with Libevent 2.1.12-stable, OpenSSL 1.1.1w, Zlib 1.3.1, Liblzma 5.8.1, Libzstd 1.5.7 and Glibc 2.41 as libc.
Jun 12 18:36:05.022 [notice] Tor can't help you if you use it wrong! Learn how to be safe at https://support.torproject.org/faq/staying-anonymous/
Jun 12 18:36:05.022 [warn] Tor was compiled with zstd 1.4.8, but is running with zstd 1.5.7. For safety, we'll avoid using advanced zstd functionality.
Jun 12 18:36:05.022 [notice] Read configuration file "/home/<USER>/Documents/augment-projects/wormv2/c2/tor/torrc".
Jun 12 18:36:05.022 [notice] Converting FascistFirewall and FirewallPorts config options to new format: "ReachableAddresses *:80,*:443,*:9050,*:9051"
Jun 12 18:36:05.023 [notice] Wow!  I detected that you have 24 CPUs. I will not autodetect any more than 16, though.  If you want to configure more, set NumCPUs in your torrc
Jun 12 18:36:05.023 [notice] Opening Socks listener on 127.0.0.1:9050
Jun 12 18:36:05.023 [notice] Opened Socks listener connection (ready) on 127.0.0.1:9050
Jun 12 18:36:05.023 [notice] Opening Control listener on 127.0.0.1:9051
Jun 12 18:36:05.023 [notice] Opened Control listener connection (ready) on 127.0.0.1:9051
Jun 12 18:36:05.023 [warn] Your log may contain sensitive information - you disabled SafeLogging. Don't log unless it serves an important reason. Overwrite the log afterwards.
Jun 12 18:36:05.028 [warn] Unhandled OpenSSL errors found at ../src/lib/tls/tortls.c:190: 
Jun 12 18:36:05.028 [warn] TLS error: could not load the shared library (in DSO support routines:dlfcn_load:---)
Jun 12 18:36:05.028 [warn] TLS error: could not load the shared library (in DSO support routines:DSO_load:---)
Jun 12 18:36:05.028 [warn] TLS error: error loading dso (in configuration file routines:module_load_dso:---)
Jun 12 18:36:05.028 [warn] TLS error: unknown module name (in configuration file routines:module_run:---)
Jun 12 18:36:05.028 [notice] Bootstrapped 0% (starting): Starting
Jun 12 18:36:05.332 [notice] Starting with guard context "default"
Jun 12 18:36:05.336 [notice] Bootstrapped 5% (conn): Connecting to a relay
Jun 12 18:36:06.391 [notice] Bootstrapped 10% (conn_done): Connected to a relay
Jun 12 18:36:06.443 [notice] Bootstrapped 14% (handshake): Handshaking with a relay
Jun 12 18:36:06.569 [notice] Bootstrapped 15% (handshake_done): Handshake with a relay done
Jun 12 18:36:06.569 [notice] Bootstrapped 75% (enough_dirinfo): Loaded enough directory info to build circuits
Jun 12 18:36:06.570 [notice] Bootstrapped 90% (ap_handshake_done): Handshake finished with a relay to build circuits
Jun 12 18:36:06.570 [notice] Bootstrapped 95% (circuit_create): Establishing a Tor circuit
Jun 12 18:36:06.885 [notice] Bootstrapped 100% (done): Done
Jun 12 18:36:14.974 [notice] Catching signal TERM, exiting cleanly.
