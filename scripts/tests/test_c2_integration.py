#!/usr/bin/env python3
"""
Integration test for C2 infrastructure
Tests actual startup and connectivity of all C2 components
"""

import os
import sys
import time
import signal
import socket
import subprocess
import requests
from pathlib import Path
import sqlite3

PROJECT_ROOT = Path(__file__).parent.parent.parent

def test_c2_startup():
    """Test actual C2 server startup and component integration"""
    print("=== Testing C2 Infrastructure Integration ===")
    
    # Start C2 server in background
    print("[+] Starting C2 server...")
    c2_process = subprocess.Popen([
        sys.executable, str(PROJECT_ROOT / "c2_server.py")
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
       preexec_fn=os.setsid, cwd=str(PROJECT_ROOT))
    
    try:
        # Wait for services to start
        print("[+] Waiting for services to initialize...")
        time.sleep(15)
        
        # Test Tor SOCKS proxy
        if test_tor_connectivity():
            print("[+] Tor service: RUNNING")
        else:
            print("[-] Tor service: FAILED")
            return False
        
        # Test web server
        if test_web_server():
            print("[+] Web server: RUNNING")
        else:
            print("[-] Web server: FAILED")
            return False
        
        # Test database connectivity
        if test_database_connectivity():
            print("[+] Database: ACCESSIBLE")
        else:
            print("[-] Database: FAILED")
            return False
        
        # Test onion services
        if test_onion_services():
            print("[+] Onion services: GENERATED")
        else:
            print("[-] Onion services: FAILED")
            return False
        
        print("\n[+] C2 infrastructure integration test PASSED!")
        return True
        
    finally:
        # Clean shutdown
        print("\n[+] Shutting down C2 infrastructure...")
        try:
            os.killpg(os.getpgid(c2_process.pid), signal.SIGTERM)
            c2_process.wait(timeout=10)
        except:
            os.killpg(os.getpgid(c2_process.pid), signal.SIGKILL)

def test_tor_connectivity():
    """Test Tor SOCKS proxy connectivity"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('127.0.0.1', 9050))
        sock.close()
        return result == 0
    except:
        return False

def test_web_server():
    """Test web server HTTP connectivity"""
    try:
        response = requests.get('http://127.0.0.1:8080/api', timeout=5)
        return response.status_code == 200
    except:
        return False

def test_database_connectivity():
    """Test database accessibility"""
    try:
        db_path = PROJECT_ROOT / "c2" / "database" / "victims.db"
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM victims")
        conn.close()
        return True
    except:
        return False

def test_onion_services():
    """Test onion service generation"""
    try:
        hostname_files = [
            PROJECT_ROOT / "c2" / "tor" / "hidden_service" / "hostname",
            PROJECT_ROOT / "c2" / "tor" / "sliver_service" / "hostname",
            PROJECT_ROOT / "c2" / "tor" / "empire_service" / "hostname"
        ]
        
        # Wait a bit more for onion services to generate
        time.sleep(10)
        
        generated_count = 0
        for hostname_file in hostname_files:
            if hostname_file.exists():
                generated_count += 1
        
        return generated_count >= 1  # At least one service should be generated
    except:
        return False

def create_test_payloads():
    """Create test payloads for web server testing"""
    print("\n=== Creating Test Payloads ===")
    
    # Windows test payload
    windows_payload = PROJECT_ROOT / "payloads" / "windows" / "test_payload.exe"
    windows_payload.write_text("# Test Windows payload\necho 'Windows payload executed'")
    print("[+] Created Windows test payload")
    
    # Linux test payload
    linux_payload = PROJECT_ROOT / "payloads" / "linux" / "test_payload.sh"
    linux_payload.write_text("#!/bin/bash\n# Test Linux payload\necho 'Linux payload executed'")
    os.chmod(linux_payload, 0o755)
    print("[+] Created Linux test payload")
    
    return True

def test_payload_hosting():
    """Test payload hosting via web server"""
    print("\n=== Testing Payload Hosting ===")
    
    try:
        # Test payload directory listing
        response = requests.get('http://127.0.0.1:8080/', timeout=5)
        if response.status_code == 200:
            print("[+] Payload directory accessible")
            
            # Test specific payload download
            response = requests.get('http://127.0.0.1:8080/windows/test_payload.exe', timeout=5)
            if response.status_code == 200:
                print("[+] Windows payload downloadable")
            else:
                print("[-] Windows payload not accessible")
                return False
                
            response = requests.get('http://127.0.0.1:8080/linux/test_payload.sh', timeout=5)
            if response.status_code == 200:
                print("[+] Linux payload downloadable")
            else:
                print("[-] Linux payload not accessible")
                return False
                
            return True
        else:
            print("[-] Payload directory not accessible")
            return False
            
    except Exception as e:
        print(f"[-] Payload hosting test error: {e}")
        return False

def run_full_integration_test():
    """Run complete integration test"""
    print("=== Phase 1 Complete Integration Test ===\n")
    
    # Create test payloads first
    if not create_test_payloads():
        print("[-] Failed to create test payloads")
        return False
    
    # Test C2 infrastructure startup
    if not test_c2_startup():
        print("[-] C2 infrastructure integration test FAILED")
        return False
    
    print("\n[+] Phase 1 implementation COMPLETE!")
    print("[+] All checkpoints passed:")
    print("    ✓ Checkpoint 1.1: Development Environment")
    print("    ✓ Checkpoint 1.2: C2 Infrastructure")
    print("\n[+] Ready to proceed to Phase 2: Universal Loader Development")
    
    return True

if __name__ == "__main__":
    success = run_full_integration_test()
    sys.exit(0 if success else 1)
