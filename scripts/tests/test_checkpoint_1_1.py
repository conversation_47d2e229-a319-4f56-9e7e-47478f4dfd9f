#!/usr/bin/env python3
"""
Test script for Checkpoint 1.1: Development Environment
Verifies all required tools are accessible and environment is properly set up
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent.parent.parent

def test_tool_availability():
    """Test that all required penetration testing tools are available"""
    print("=== Testing Tool Availability ===")
    
    required_tools = [
        ("tor", "/usr/sbin/tor"),
        ("sliver-server", "/usr/bin/sliver-server"),
        ("sliver-client", "/usr/bin/sliver-client"),
        ("powershell-empire", "/usr/bin/powershell-empire"),
        ("sqlite3", "/usr/bin/sqlite3"),
        ("nginx", "/usr/sbin/nginx"),
        ("python3", "/usr/bin/python3"),
        ("git", "/usr/bin/git"),
        ("nmap", "/usr/bin/nmap"),
        ("socat", "/usr/bin/socat")
    ]
    
    missing_tools = []
    
    for tool_name, expected_path in required_tools:
        if shutil.which(tool_name):
            print(f"[+] {tool_name}: Available")
        else:
            print(f"[-] {tool_name}: Missing")
            missing_tools.append(tool_name)
    
    if missing_tools:
        print(f"\n[-] Missing tools: {', '.join(missing_tools)}")
        return False
    else:
        print("\n[+] All required tools are available")
        return True

def test_tool_execution():
    """Test that tools can be executed with proper permissions"""
    print("\n=== Testing Tool Execution Permissions ===")
    
    tool_tests = [
        ("tor", ["tor", "--version"]),
        ("sqlite3", ["sqlite3", "--version"]),
        ("python3", ["python3", "--version"]),
        ("git", ["git", "--version"]),
        ("nmap", ["nmap", "--version"])
    ]
    
    failed_tests = []
    
    for tool_name, cmd in tool_tests:
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                print(f"[+] {tool_name}: Executable")
            else:
                print(f"[-] {tool_name}: Execution failed")
                failed_tests.append(tool_name)
                
        except Exception as e:
            print(f"[-] {tool_name}: Error - {e}")
            failed_tests.append(tool_name)
    
    if failed_tests:
        print(f"\n[-] Failed execution tests: {', '.join(failed_tests)}")
        return False
    else:
        print("\n[+] All tool execution tests passed")
        return True

def test_python_environment():
    """Test Python virtual environment and dependencies"""
    print("\n=== Testing Python Environment ===")
    
    # Check virtual environment
    venv_path = PROJECT_ROOT / "venv"
    if venv_path.exists():
        print("[+] Virtual environment exists")
    else:
        print("[-] Virtual environment missing")
        return False
    
    # Check Python executable in venv
    venv_python = venv_path / "bin" / "python3"
    if venv_python.exists():
        print("[+] Python executable in venv")
    else:
        print("[-] Python executable missing in venv")
        return False
    
    # Test importing required modules
    test_imports = [
        "requests",
        "stem",
        "flask",
        "cryptography",
        "paramiko",
        "psutil",
        "sqlite3"
    ]
    
    failed_imports = []
    
    for module in test_imports:
        try:
            result = subprocess.run([
                str(venv_python),
                "-c", f"import {module}; print('{module} imported successfully')"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"[+] {module}: Available")
            else:
                print(f"[-] {module}: Import failed")
                failed_imports.append(module)
                
        except Exception as e:
            print(f"[-] {module}: Error - {e}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n[-] Failed imports: {', '.join(failed_imports)}")
        return False
    else:
        print("\n[+] All Python dependencies available")
        return True

def test_directory_structure():
    """Test that project directory structure is correct"""
    print("\n=== Testing Directory Structure ===")
    
    required_dirs = [
        "c2/sliver",
        "c2/empire", 
        "c2/tor",
        "c2/database",
        "payloads/windows",
        "payloads/linux",
        "tools",
        "logs",
        "scripts/tests",
        "scripts/setup",
        "config",
        "docs"
    ]
    
    missing_dirs = []
    
    for dir_path in required_dirs:
        full_path = PROJECT_ROOT / dir_path
        if full_path.exists() and full_path.is_dir():
            print(f"[+] {dir_path}: Exists")
        else:
            print(f"[-] {dir_path}: Missing")
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print(f"\n[-] Missing directories: {', '.join(missing_dirs)}")
        return False
    else:
        print("\n[+] All required directories exist")
        return True

def test_git_repository():
    """Test Git repository initialization"""
    print("\n=== Testing Git Repository ===")
    
    git_dir = PROJECT_ROOT / ".git"
    if git_dir.exists():
        print("[+] Git repository initialized")
    else:
        print("[-] Git repository not initialized")
        return False
    
    # Test .gitignore
    gitignore = PROJECT_ROOT / ".gitignore"
    if gitignore.exists():
        print("[+] .gitignore exists")
        
        # Check for security-sensitive patterns
        with open(gitignore, 'r') as f:
            content = f.read()
            
        security_patterns = ["*.key", "*.log", "*.db", "hidden_service"]
        missing_patterns = []
        
        for pattern in security_patterns:
            if pattern in content:
                print(f"[+] Security pattern '{pattern}' in .gitignore")
            else:
                print(f"[-] Security pattern '{pattern}' missing from .gitignore")
                missing_patterns.append(pattern)
        
        if missing_patterns:
            print(f"\n[-] Missing security patterns: {', '.join(missing_patterns)}")
            return False
    else:
        print("[-] .gitignore missing")
        return False
    
    print("\n[+] Git repository properly configured")
    return True

def test_file_permissions():
    """Test that sensitive files have proper permissions"""
    print("\n=== Testing File Permissions ===")
    
    # Test script executability
    scripts_to_test = [
        "c2_server.py",
        "c2/database/init_db.py",
        "scripts/tests/test_checkpoint_1_1.py"
    ]
    
    permission_issues = []
    
    for script_path in scripts_to_test:
        full_path = PROJECT_ROOT / script_path
        if full_path.exists():
            # Check if file is readable
            if os.access(full_path, os.R_OK):
                print(f"[+] {script_path}: Readable")
            else:
                print(f"[-] {script_path}: Not readable")
                permission_issues.append(script_path)
        else:
            print(f"[-] {script_path}: File missing")
            permission_issues.append(script_path)
    
    if permission_issues:
        print(f"\n[-] Permission issues: {', '.join(permission_issues)}")
        return False
    else:
        print("\n[+] All file permissions correct")
        return True

def run_all_tests():
    """Run all Checkpoint 1.1 tests"""
    print("=== Checkpoint 1.1: Development Environment Tests ===\n")
    
    tests = [
        ("Tool Availability", test_tool_availability),
        ("Tool Execution", test_tool_execution),
        ("Python Environment", test_python_environment),
        ("Directory Structure", test_directory_structure),
        ("Git Repository", test_git_repository),
        ("File Permissions", test_file_permissions)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed_tests += 1
            print(f"[+] {test_name}: PASSED")
        else:
            print(f"[-] {test_name}: FAILED")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed_tests}/{total_tests}")
    print(f"Failed: {total_tests - passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("\n[+] All Checkpoint 1.1 tests PASSED!")
        print("[+] Development environment is ready")
        return True
    else:
        print("\n[-] Some Checkpoint 1.1 tests FAILED!")
        print("[-] Please fix issues before proceeding")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
