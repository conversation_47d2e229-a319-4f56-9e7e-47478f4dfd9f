#!/usr/bin/env python3
"""
Test script for Checkpoint 1.2: C2 Infrastructure
Tests Tor hidden service, Sliver C2, Empire framework, and web server components
"""

import os
import sys
import time
import socket
import subprocess
import requests
from pathlib import Path
import sqlite3

PROJECT_ROOT = Path(__file__).parent.parent.parent

def test_tor_service():
    """Test Tor hidden service configuration and startup"""
    print("=== Testing Tor Hidden Service ===")
    
    # Check Tor configuration file
    torrc_path = PROJECT_ROOT / "c2" / "tor" / "torrc"
    if not torrc_path.exists():
        print("[-] Tor configuration file missing")
        return False
    
    print("[+] Tor configuration file exists")
    
    # Check Tor directories
    tor_dirs = [
        PROJECT_ROOT / "c2" / "tor" / "data",
        PROJECT_ROOT / "c2" / "tor" / "hidden_service",
        PROJECT_ROOT / "c2" / "tor" / "sliver_service",
        PROJECT_ROOT / "c2" / "tor" / "empire_service"
    ]
    
    for tor_dir in tor_dirs:
        if tor_dir.exists():
            print(f"[+] {tor_dir.name} directory exists")
        else:
            print(f"[-] {tor_dir.name} directory missing")
            return False
    
    # Test Tor startup (brief test)
    print("[+] Testing Tor startup...")
    try:
        tor_process = subprocess.Popen([
            "tor", "-f", str(torrc_path), "--verify-config"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        stdout, stderr = tor_process.communicate(timeout=10)
        
        if tor_process.returncode == 0:
            print("[+] Tor configuration is valid")
        else:
            print(f"[-] Tor configuration error: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"[-] Error testing Tor: {e}")
        return False
    
    print("[+] Tor hidden service test passed")
    return True

def test_database_operations():
    """Test SQLite database operations"""
    print("\n=== Testing Database Operations ===")
    
    db_path = PROJECT_ROOT / "c2" / "database" / "victims.db"
    if not db_path.exists():
        print("[-] Database file missing")
        return False
    
    print("[+] Database file exists")
    
    try:
        # Test database connection
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Test table existence
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        
        required_tables = [
            'victims', 'commands', 'credentials', 'network_hosts',
            'files', 'propagation', 'social_accounts', 'persistence'
        ]
        
        missing_tables = []
        for table in required_tables:
            if table in tables:
                print(f"[+] Table '{table}' exists")
            else:
                print(f"[-] Table '{table}' missing")
                missing_tables.append(table)
        
        if missing_tables:
            print(f"[-] Missing tables: {', '.join(missing_tables)}")
            conn.close()
            return False
        
        # Test basic operations
        cursor.execute("""
            INSERT INTO victims (uuid, hostname, ip_address, os_type, username)
            VALUES (?, ?, ?, ?, ?)
        """, ("test-db-123", "test-host", "*************", "Linux", "testuser"))
        
        cursor.execute("SELECT COUNT(*) FROM victims WHERE uuid = ?", ("test-db-123",))
        count = cursor.fetchone()[0]
        
        if count == 1:
            print("[+] Database insert/select operations working")
            
            # Clean up
            cursor.execute("DELETE FROM victims WHERE uuid = ?", ("test-db-123",))
            conn.commit()
            print("[+] Database cleanup successful")
        else:
            print("[-] Database operations failed")
            conn.close()
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"[-] Database test error: {e}")
        return False
    
    print("[+] Database operations test passed")
    return True

def test_port_availability():
    """Test that required ports are available for C2 services"""
    print("\n=== Testing Port Availability ===")
    
    required_ports = [
        (8080, "Web server HTTP"),
        (8443, "Web server HTTPS"),
        (31337, "Sliver C2"),
        (1337, "Empire C2"),
        (9050, "Tor SOCKS"),
        (9051, "Tor Control")
    ]
    
    unavailable_ports = []
    
    for port, description in required_ports:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        
        try:
            result = sock.connect_ex(('127.0.0.1', port))
            if result == 0:
                print(f"[-] Port {port} ({description}): In use")
                unavailable_ports.append(port)
            else:
                print(f"[+] Port {port} ({description}): Available")
        except Exception as e:
            print(f"[+] Port {port} ({description}): Available (error: {e})")
        finally:
            sock.close()
    
    if unavailable_ports:
        print(f"\n[-] Ports in use: {', '.join(map(str, unavailable_ports))}")
        print("[-] Stop services using these ports before starting C2")
        return False
    else:
        print("\n[+] All required ports are available")
        return True

def test_nginx_config():
    """Test nginx configuration generation"""
    print("\n=== Testing Nginx Configuration ===")
    
    try:
        # Test nginx config syntax
        config_path = PROJECT_ROOT / "config" / "nginx.conf"
        
        if not config_path.exists():
            print("[-] Nginx configuration file missing")
            return False
        
        print("[+] Nginx configuration file exists")
        
        # Test nginx config syntax
        result = subprocess.run([
            "nginx", "-t", "-c", str(config_path)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("[+] Nginx configuration syntax is valid")
        else:
            print(f"[-] Nginx configuration error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"[-] Error testing nginx config: {e}")
        return False
    
    print("[+] Nginx configuration test passed")
    return True

def test_payload_directories():
    """Test payload hosting directory structure"""
    print("\n=== Testing Payload Directories ===")
    
    payload_dirs = [
        PROJECT_ROOT / "payloads" / "windows",
        PROJECT_ROOT / "payloads" / "linux"
    ]
    
    for payload_dir in payload_dirs:
        if payload_dir.exists() and payload_dir.is_dir():
            print(f"[+] {payload_dir.name} payload directory exists")
            
            # Check if directory is writable
            test_file = payload_dir / "test_write.tmp"
            try:
                test_file.write_text("test")
                test_file.unlink()
                print(f"[+] {payload_dir.name} directory is writable")
            except Exception as e:
                print(f"[-] {payload_dir.name} directory not writable: {e}")
                return False
        else:
            print(f"[-] {payload_dir.name} payload directory missing")
            return False
    
    print("[+] Payload directories test passed")
    return True

def test_log_directory():
    """Test log directory setup"""
    print("\n=== Testing Log Directory ===")
    
    logs_dir = PROJECT_ROOT / "logs"
    if not logs_dir.exists():
        print("[-] Logs directory missing")
        return False
    
    print("[+] Logs directory exists")
    
    # Test log file creation
    test_log = logs_dir / "test.log"
    try:
        test_log.write_text("Test log entry\n")
        if test_log.exists():
            print("[+] Log file creation successful")
            test_log.unlink()
        else:
            print("[-] Log file creation failed")
            return False
    except Exception as e:
        print(f"[-] Log directory test error: {e}")
        return False
    
    print("[+] Log directory test passed")
    return True

def test_c2_server_script():
    """Test C2 server script syntax and imports"""
    print("\n=== Testing C2 Server Script ===")
    
    c2_script = PROJECT_ROOT / "c2_server.py"
    if not c2_script.exists():
        print("[-] C2 server script missing")
        return False
    
    print("[+] C2 server script exists")
    
    # Test script syntax
    try:
        result = subprocess.run([
            "python3", "-m", "py_compile", str(c2_script)
        ], capture_output=True, text=True, cwd=str(PROJECT_ROOT))
        
        if result.returncode == 0:
            print("[+] C2 server script syntax is valid")
        else:
            print(f"[-] C2 server script syntax error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"[-] Error testing C2 server script: {e}")
        return False
    
    print("[+] C2 server script test passed")
    return True

def run_all_tests():
    """Run all Checkpoint 1.2 tests"""
    print("=== Checkpoint 1.2: C2 Infrastructure Tests ===\n")
    
    tests = [
        ("Tor Service", test_tor_service),
        ("Database Operations", test_database_operations),
        ("Port Availability", test_port_availability),
        ("Nginx Configuration", test_nginx_config),
        ("Payload Directories", test_payload_directories),
        ("Log Directory", test_log_directory),
        ("C2 Server Script", test_c2_server_script)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed_tests += 1
            print(f"[+] {test_name}: PASSED")
        else:
            print(f"[-] {test_name}: FAILED")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed_tests}/{total_tests}")
    print(f"Failed: {total_tests - passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("\n[+] All Checkpoint 1.2 tests PASSED!")
        print("[+] C2 infrastructure is ready for deployment")
        return True
    else:
        print("\n[-] Some Checkpoint 1.2 tests FAILED!")
        print("[-] Please fix issues before proceeding")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
